# Vercel Deployment Configuration

## Required Environment Variables

### Database
```
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require
```

### Authentication
```
NEXTAUTH_SECRET=your-nextauth-secret-key
NEXTAUTH_URL=https://your-vercel-domain.vercel.app
```

### Deriv API
```
DERIV_API_TOKEN=your-deriv-api-token
DERIV_ACCOUNT_ID=your-deriv-account-id
```

### Optional
```
NODE_ENV=production
PRISMA_GENERATE_DATAPROXY=true
```

## Vercel Configuration

The `vercel.json` file includes:
- Extended function timeout (30s) for trade execution
- Prisma Data Proxy configuration
- Optimized regions (iad1)

## Deployment Steps

1. **Set Environment Variables**: Add all required env vars in Vercel dashboard
2. **Database Setup**: Ensure PostgreSQL database is accessible from Vercel
3. **Prisma Migration**: Run `npx prisma migrate deploy` in Vercel build
4. **Test Deployment**: Verify all API routes work in production

## Known Issues & Solutions

### Stake Precision Error
- **Issue**: Deriv API requires max 2 decimal places for stake amounts
- **Solution**: Implemented proper rounding in all stake calculations
- **Status**: ✅ FIXED

### WebSocket Connections
- **Issue**: Concurrent WebSocket connections may hit Vercel limits
- **Solution**: Implemented connection pooling and proper cleanup
- **Status**: ⚠️ MONITOR

### Database Connections
- **Issue**: Serverless functions need proper connection management
- **Solution**: Using Prisma Data Proxy for connection pooling
- **Status**: ✅ CONFIGURED

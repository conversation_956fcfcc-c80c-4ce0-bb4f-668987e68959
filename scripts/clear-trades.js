const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearTrades() {
  try {
    console.log('🗑️  Clearing all existing trades from database...');
    
    const deleteResult = await prisma.trade.deleteMany({});
    
    console.log(`✅ Successfully deleted ${deleteResult.count} trades from database`);
    console.log('🔄 Database is now clean and ready for new trade data structure');
    
  } catch (error) {
    console.error('❌ Error clearing trades:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearTrades();
